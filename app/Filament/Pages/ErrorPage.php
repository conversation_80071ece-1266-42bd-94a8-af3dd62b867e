<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class ErrorPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-exclamation-triangle';
    protected static bool $shouldRegisterNavigation = false;
    protected static string $view = 'filament.pages.error-page';

    public string $errorCode = '500';
    public string $errorTitle = '';
    public string $errorMessage = '';
    public string $errorDescription = '';
    public string $iconClass = '';
    public string $iconBgClass = '';

    public function mount(
        string $errorCode = '500',
        string $errorTitle = '',
        string $errorMessage = '',
        string $errorDescription = '',
        string $iconClass = '',
        string $iconBgClass = ''
    ): void {
        $this->errorCode = $errorCode;
        $this->errorTitle = $errorTitle ?: __('Error');
        $this->errorMessage = $errorMessage ?: __('An error occurred.');
        $this->errorDescription = $errorDescription ?: __('Please try again or contact support if the problem persists.');
        $this->iconClass = $iconClass ?: 'heroicon-o-exclamation-triangle';
        $this->iconBgClass = $iconBgClass ?: 'bg-red-100 dark:bg-red-900/20';
    }

    public function getTitle(): string
    {
        return $this->errorTitle;
    }

    public function getHeading(): string
    {
        return $this->errorTitle;
    }

    protected function getViewData(): array
    {
        return [
            'errorCode' => $this->errorCode,
            'errorTitle' => $this->errorTitle,
            'errorMessage' => $this->errorMessage,
            'errorDescription' => $this->errorDescription,
            'iconClass' => $this->iconClass,
            'iconBgClass' => $this->iconBgClass,
        ];
    }
}
