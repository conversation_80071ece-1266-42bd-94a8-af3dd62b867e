<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class ErrorPageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Use the same logic as Khaleds\FilamentTranslations\Http\Middleware\LanguageMiddleware

        // First check if user is not authenticated but has cookie
        if (!$request->user() && $request->cookie('filament_language_switch_locale')) {
            app()->setLocale($request->cookie('filament_language_switch_locale'));
            return $next($request);
        }

        // If user is authenticated
        if ($request->user()) {
            $locale = $request->user()->lang;

            // <PERSON><PERSON> takes precedence over user lang
            if ($locale != $request->cookie('filament_language_switch_locale')) {
                $locale = $request->cookie('filament_language_switch_locale');
            }

            // Fallback to app locale if null
            if (is_null($locale)) {
                $locale = app()->getLocale();
            }

            app()->setLocale($locale);
            return $next($request);
        }

        // Final fallback
        app()->setLocale(config('app.locale', 'en'));
        return $next($request);
    }
}
