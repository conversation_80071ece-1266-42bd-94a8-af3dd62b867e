<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Filament\Facades\Filament;

class ErrorController extends Controller
{
    public function error404(Request $request)
    {
        return response()->view('errors.custom.404', [
            'exception' => null,
            'request' => $request
        ], 404);
    }

    public function error403(Request $request)
    {
        return response()->view('errors.custom.403', [
            'exception' => null,
            'request' => $request
        ], 403);
    }

    public function error419(Request $request)
    {
        return response()->view('errors.custom.419', [
            'exception' => null,
            'request' => $request
        ], 419);
    }

    public function error500(Request $request)
    {
        return response()->view('errors.custom.500', [
            'exception' => null,
            'request' => $request
        ], 500);
    }

    public function error503(Request $request)
    {
        return response()->view('errors.custom.503', [
            'exception' => null,
            'request' => $request
        ], 503);
    }

    public function errorMinimal(Request $request)
    {
        return response()->view('errors.custom.minimal', [
            'exception' => null,
            'request' => $request
        ], 500);
    }
}
