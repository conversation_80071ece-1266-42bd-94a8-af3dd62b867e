@extends('errors.filament-layout')

@section('title', __('Error'))

@section('icon-bg-color', 'bg-gray-100 dark:bg-gray-900/20')

@section('icon')
<svg class="h-8 w-8 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
</svg>
@endsection

@section('heading', __('Error'))

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-gray-500">❗</span>
    {{ isset($exception) && $exception->getMessage() ? $exception->getMessage() : __('An error occurred.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('Please try again or contact support if the problem persists.') }}
</p>
@endsection

@section('actions')
<button onclick="window.location.reload()"
   class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action w-full">
    {{ __('Try Again') }}
</button>

<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-gray fi-color-gray fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-white text-gray-950 hover:bg-gray-50 focus-visible:ring-primary-500/50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 dark:focus-visible:ring-primary-400/50 ring-1 ring-gray-950/10 dark:ring-white/20 fi-ac-action fi-ac-btn-action w-full">
    {{ __('Back to Dashboard') }}
</a>
@endsection

@section('error-code', '')
    <div class="min-h-[60vh] flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <!-- Error Icon -->
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-900/20 animate-pulse">
                    <svg class="h-8 w-8 text-gray-600 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="text-center space-y-4">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Error') }}
                </h1>

                <div class="space-y-2">
                    <p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
                        <span class="text-gray-500">❗</span>
                        {{ isset($exception) && $exception->getMessage() ? $exception->getMessage() : __('An error occurred.') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Please try again or contact support if the problem persists.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <x-filament::button
                    onclick="window.location.reload()"
                    color="primary"
                    size="lg"
                    class="w-full"
                >
                    {{ __('Try Again') }}
                </x-filament::button>

                <x-filament::button
                    href="{{ route('filament.admin.pages.dashboard') }}"
                    color="gray"
                    size="lg"
                    class="w-full"
                    outlined
                >
                    {{ __('Back to Dashboard') }}
                </x-filament::button>
            </div>

            <!-- Additional Info -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
