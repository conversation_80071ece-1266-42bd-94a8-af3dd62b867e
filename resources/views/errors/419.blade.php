@extends('errors.filament-layout')

@section('title', __('Session Expired'))

@section('icon-bg-color', 'bg-yellow-100 dark:bg-yellow-900/20')

@section('icon')
<svg class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
</svg>
@endsection

@section('heading', __('Session Expired'))

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-primary-600">⏰</span>
    {{ __('Your session has expired for security reasons.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('Please refresh the page and try again.') }}
</p>
@endsection

@section('actions')
<button onclick="window.location.reload()"
   class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action w-full">
    {{ __('Refresh Page') }}
</button>

<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-gray fi-color-gray fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-white text-gray-950 hover:bg-gray-50 focus-visible:ring-primary-500/50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 dark:focus-visible:ring-primary-400/50 ring-1 ring-gray-950/10 dark:ring-white/20 fi-ac-action fi-ac-btn-action w-full">
    {{ __('Back to Dashboard') }}
</a>
@endsection

@section('error-code', __('Error Code: 419'))
