<x-filament-panels::page>
    <div class="min-h-[60vh] flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <!-- Error Icon -->
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 animate-pulse">
                    <svg class="h-8 w-8 text-[#246250]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="text-center space-y-4">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Server Error') }}
                </h1>

                <div class="space-y-2">
                    <p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
                        <span class="text-[#246250]">⚠️</span>
                        {{ __('Something went wrong on our servers.') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('We\'re working to fix this issue. Please try again later.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <x-filament::button
                    onclick="window.location.reload()"
                    color="primary"
                    size="lg"
                    class="w-full"
                >
                    {{ __('Try Again') }}
                </x-filament::button>

                <x-filament::button
                    href="{{ route('filament.admin.pages.dashboard') }}"
                    color="gray"
                    size="lg"
                    class="w-full"
                    outlined
                >
                    {{ __('Back to Dashboard') }}
                </x-filament::button>

                @if(\Illuminate\Support\Facades\Route::has('filament.admin.resources.tickets.index'))
                    <x-filament::button
                        href="{{ route('filament.admin.resources.tickets.index') }}"
                        color="gray"
                        size="lg"
                        class="w-full"
                        outlined
                    >
                        {{ __('Report Issue') }}
                    </x-filament::button>
                @endif
            </div>

            <!-- Additional Info -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    {{ __('Error Code:') }} 500 • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
