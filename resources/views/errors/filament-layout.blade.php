<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" class="fi fi-color-custom fi-body-bg-gray-50 dark:fi-body-bg-gray-950">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', __('Error')) - {{ config('app.name', 'Laravel') }}</title>
    
    @filamentStyles
    @vite(['resources/css/customTheme.css'])
    
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="fi-body fi-panel-admin min-h-screen bg-gray-50 font-normal text-gray-950 antialiased dark:bg-gray-950 dark:text-white">
    <div class="fi-layout flex min-h-screen w-full overflow-x-clip">
        <div class="fi-main-ctn flex w-full flex-col opacity-100">
            <main class="fi-main mx-auto h-full w-full px-4 md:px-6 lg:px-8 max-w-7xl">
                <div class="fi-page">
                    <div class="min-h-[60vh] flex items-center justify-center py-12">
                        <div class="max-w-md w-full space-y-8">
                            <!-- Error Icon -->
                            <div class="text-center">
                                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full @yield('icon-bg-color', 'bg-gray-100 dark:bg-gray-900/20') animate-pulse">
                                    @yield('icon')
                                </div>
                            </div>

                            <!-- Error Content -->
                            <div class="text-center space-y-4">
                                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                                    @yield('heading')
                                </h1>

                                <div class="space-y-2">
                                    @yield('message')
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="space-y-3">
                                @yield('actions')
                            </div>

                            <!-- Additional Info -->
                            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                                    @yield('error-code') • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    @filamentScripts
</body>
</html>
