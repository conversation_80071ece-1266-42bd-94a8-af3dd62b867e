@php
    $errorPage = new \App\Filament\Pages\ErrorPage();
    $errorPage->mount(
        errorCode: '404',
        errorTitle: __('Page Not Found'),
        errorMessage: __('The page you are looking for doesn\'t exist or has been moved.'),
        errorDescription: __('You can return to the dashboard or contact support.'),
        iconClass: 'search',
        iconBgClass: 'bg-blue-100 dark:bg-blue-900/20'
    );
@endphp

<x-filament-panels::page>
    <div class="min-h-[60vh] flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <!-- Error Icon -->
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/20 animate-pulse">
                    <svg class="h-8 w-8 text-[#246250]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="text-center space-y-4">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Page Not Found') }}
                </h1>

                <div class="space-y-2">
                    <p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
                        <span class="text-[#246250]">🔍</span>
                        {{ __('The page you are looking for doesn\'t exist or has been moved.') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('You can return to the dashboard or contact support.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <x-filament::button
                    href="{{ route('filament.admin.pages.dashboard') }}"
                    color="primary"
                    size="lg"
                    class="w-full"
                >
                    {{ __('Back to Dashboard') }}
                </x-filament::button>

                @if(\Illuminate\Support\Facades\Route::has('filament.admin.resources.tickets.index'))
                    <x-filament::button
                        href="{{ route('filament.admin.resources.tickets.index') }}"
                        color="gray"
                        size="lg"
                        class="w-full"
                        outlined
                    >
                        {{ __('Open Support Ticket') }}
                    </x-filament::button>
                @endif
            </div>

            <!-- Additional Info -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    {{ __('Error Code:') }} 404 • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
