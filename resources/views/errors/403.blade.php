<x-filament-panels::page>
    <div class="min-h-[60vh] flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <!-- Error Icon -->
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 animate-pulse">
                    <svg class="h-8 w-8 text-[#246250]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="text-center space-y-4">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Access Forbidden') }}
                </h1>

                <div class="space-y-2">
                    <p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
                        <span class="text-[#246250]">🚫</span>
                        {{ __('You don\'t have permission to access this resource.') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('Please contact your administrator if you believe this is an error.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <x-filament::button
                    href="{{ route('filament.admin.pages.dashboard') }}"
                    color="primary"
                    size="lg"
                    class="w-full"
                >
                    {{ __('Back to Dashboard') }}
                </x-filament::button>

                @if(\Illuminate\Support\Facades\Route::has('filament.admin.resources.tickets.index'))
                    <x-filament::button
                        href="{{ route('filament.admin.resources.tickets.index') }}"
                        color="gray"
                        size="lg"
                        class="w-full"
                        outlined
                    >
                        {{ __('Contact Support') }}
                    </x-filament::button>
                @endif
            </div>

            <!-- Additional Info -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    {{ __('Error Code:') }} 403 • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
