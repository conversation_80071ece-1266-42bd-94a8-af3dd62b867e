@extends('errors.filament-layout')

@section('title', __('Access Forbidden'))

@section('icon-bg-color', 'bg-red-100 dark:bg-red-900/20')

@section('icon')
<svg class="h-8 w-8 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
</svg>
@endsection

@section('heading', __('Access Forbidden'))

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-primary-600">🚫</span>
    {{ __('You don\'t have permission to access this resource.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('Please contact your administrator if you believe this is an error.') }}
</p>
@endsection

@section('actions')
<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action w-full">
    {{ __('Back to Dashboard') }}
</a>

@if(\Illuminate\Support\Facades\Route::has('filament.admin.resources.tickets.index'))
    <a href="{{ route('filament.admin.resources.tickets.index') }}"
       class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-gray fi-color-gray fi-size-lg fi-btn-size-lg gap-1.5 px-4 py-3 text-sm inline-grid shadow-sm bg-white text-gray-950 hover:bg-gray-50 focus-visible:ring-primary-500/50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 dark:focus-visible:ring-primary-400/50 ring-1 ring-gray-950/10 dark:ring-white/20 fi-ac-action fi-ac-btn-action w-full">
        {{ __('Contact Support') }}
    </a>
@endif
@endsection

@section('error-code', __('Error Code: 403'))
