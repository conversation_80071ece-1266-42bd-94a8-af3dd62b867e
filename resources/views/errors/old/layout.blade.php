<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@yield('title', __('Error')) - {{ config('app.name', 'Laravel') }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Styles -->
    <style>
        [x-cloak] { display: none !important; }
        .rtl { direction: rtl; }
        .ltr { direction: ltr; }

        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Pulse animation for icon */
        @keyframes pulse-soft {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animate-pulse-soft {
            animation: pulse-soft 2s infinite;
        }
    </style>
</head>
<body class="antialiased bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 min-h-screen">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8 animate-fade-in-up">
            <!-- Logo Section -->
            <div class="text-center">
                <div class="mx-auto h-20 w-auto flex justify-center items-center">
                    <img class="h-16 w-auto transition-transform duration-300 hover:scale-105"
                         src="{{ asset('images/Kera_light_logo.png') }}"
                         alt="{{ config('app.name', 'Laravel') }}">
                </div>
            </div>

            <!-- Error Card -->
            <div class="bg-white dark:bg-gray-800 shadow-2xl rounded-2xl p-8 border border-gray-200 dark:border-gray-700 backdrop-blur-sm">
                <!-- Error Icon -->
                <div class="text-center mb-6">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full @yield('icon-bg-color', 'bg-red-100 dark:bg-red-900/20') animate-pulse-soft">
                        @yield('icon')
                    </div>
                </div>

                <!-- Error Content -->
                <div class="text-center space-y-4">
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        @yield('title', __('Error'))
                    </h1>

                    <div class="space-y-2">
                        @yield('message')
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-8 space-y-3">
                    @yield('actions')
                </div>

                <!-- Additional Info -->
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                        @yield('error-code') • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                    </p>
                </div>
            </div>


        </div>
    </div>

    <!-- Additional Scripts -->
    @yield('scripts')
</body>
</html>
