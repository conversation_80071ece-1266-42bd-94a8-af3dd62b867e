@extends('errors::layout')

@section('title', __('Page Not Found'))

@section('icon-bg-color', 'bg-blue-100 dark:bg-blue-900/20')

@section('icon')
<svg class="h-8 w-8 text-[#246250] " fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
</svg>
@endsection

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-[#246250]">🔍</span>
    {{ __('The page you are looking for doesn't exist or has been moved.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('You can return to the dashboard or contact support.') }}
</p>
@endsection

@section('actions')
<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#246250] hover:bg-[#1e5244] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Back to Dashboard') }}
</a>

<a href="{{ route('filament.admin.resources.tickets.index') }}"
   class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Open Support Ticket') }}
</a>
@endsection

@section('error-code', __('Error Code: 404'))
