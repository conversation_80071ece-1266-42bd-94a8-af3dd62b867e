@extends('errors::layout')

@section('title', __('Service Unavailable'))

@section('icon-bg-color', 'bg-purple-100 dark:bg-purple-900/20')

@section('icon')
<svg class="h-8 w-8 text-purple-600 dark:text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
</svg>
@endsection

@section('message')
<p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
    <span class="text-purple-500">🔧</span>
    {{ __('We\'re currently performing maintenance.') }}
</p>
<p class="text-sm text-gray-500 dark:text-gray-400">
    {{ __('The service will be back online shortly. Thank you for your patience.') }}
</p>
@endsection

@section('actions')
<a href="{{ route('filament.admin.pages.dashboard') }}"
   class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#246250] hover:bg-[#1e5244] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Back to Dashboard') }}
</a>

<button onclick="window.location.reload()"
        class="w-full flex justify-center py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#246250] transition-colors duration-200">
    {{ __('Try Again') }}
</button>
@endsection

@section('error-code', __('Error Code: 503'))
