<x-filament-panels::page>
    <div class="min-h-[60vh] flex items-center justify-center">
        <div class="max-w-md w-full space-y-8">
            <!-- Error Icon -->
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-orange-100 dark:bg-orange-900/20 animate-pulse">
                    <svg class="h-8 w-8 text-[#246250]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                </div>
            </div>

            <!-- Error Content -->
            <div class="text-center space-y-4">
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                    {{ __('Service Unavailable') }}
                </h1>

                <div class="space-y-2">
                    <p class="text-lg text-gray-600 dark:text-gray-300 flex items-center justify-center gap-2">
                        <span class="text-[#246250]">🔧</span>
                        {{ __('We\'re currently performing maintenance.') }}
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        {{ __('The service will be back online shortly. Thank you for your patience.') }}
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <x-filament::button
                    onclick="window.location.reload()"
                    color="primary"
                    size="lg"
                    class="w-full"
                >
                    {{ __('Refresh Page') }}
                </x-filament::button>

                @if(\Illuminate\Support\Facades\Route::has('filament.admin.resources.tickets.index'))
                    <x-filament::button
                        href="{{ route('filament.admin.resources.tickets.index') }}"
                        color="gray"
                        size="lg"
                        class="w-full"
                        outlined
                    >
                        {{ __('Contact Support') }}
                    </x-filament::button>
                @endif
            </div>

            <!-- Additional Info -->
            <div class="pt-6 border-t border-gray-200 dark:border-gray-700">
                <p class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    {{ __('Error Code:') }} 503 • {{ __('Time:') }} {{ now()->format('Y-m-d H:i:s') }}
                </p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
