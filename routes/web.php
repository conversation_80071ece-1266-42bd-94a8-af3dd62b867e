<?php

use App\Http\Controllers\NafathAPIController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Modules\Account\app\Models\Account;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;
use App\Models\User;
use Khaleds\FilamentTranslations\Http\Middleware\LanguageMiddleware;

Route::get('/', function () {
    return redirect('/admin');
});


Route::get('/init-time', function () {
    $initTime = microtime(true) - LARAVEL_START;
    return response()->json([
        'init_time' => $initTime,
        'init_time_ms' => $initTime * 1000,
    ]);
});


Route::get('/notification',function (){
    $template = \Khaleds\Notifications\Models\NotificationsTemplate::find(1);

    \Khaleds\Notifications\Services\SendNotification::make(['fcm-api'])
        ->template($template->key)
        ->model(Account::class)
        ->id(1)
        ->icon('bx bx-user')
        ->url(url('/'))
        ->privacy('private')
        ->database(true)
        ->fire();
});


//Email Verify

Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

Route::get('nafath/request/{national_id}', [NafathAPIController::class, 'send'])->name('nafath.send');
Route::get('nafath/resend/{national_id}', [NafathAPIController::class, 'resend'])->name('nafath.resend');
Route::get('nafath/verify', [NafathAPIController::class, 'retriveStatus'])->name('nafath.verify');

Route::get('/nafath/verify/page', function () {
    return view('auth.verify-nafath');
})->middleware('auth')->name('nafath.verify.view');

Route::get('/nafath/verify/confirm', [\App\Http\Controllers\NafathAPIController::class, 'verifyConfirm'])->name('nafath.verify.confirm');

Route::post('/nafath/verify/status', [NafathAPIController::class, 'retriveStatus'])->name('nafath.verify.status');

Route::get('/email/verify/{id}/{hash}', function ($id, $hash) {
    $user = User::find($id);

    if (!$user) {
        return redirect('/admin/login')->with('error', 'User not found');
    }

    if (!hash_equals(sha1($user->getEmailForVerification()), $hash)) {
        return redirect('/admin/login')->with('error', 'Invalid verification link');
    }

    if ($user->hasVerifiedEmail()) {
        return redirect('/admin/login')->with('message', 'Email already verified');
    }

    $user->markEmailAsVerified();

    Auth::logout();
    return redirect('/admin/login')->with('success', 'Email verified successfully');
})->middleware(['signed'])->name('verification.verify');



Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');


Route::post('/email/verification-notification', function (Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');


Route::middleware([LanguageMiddleware::class])->get('/suspended-user', function () {
    return view('vendor.filament-panels.pages.auth.suspend');
});

Route::middleware([LanguageMiddleware::class])->get('/nafath-verify', function () {
    return view('vendor.filament-panels.pages.auth.nafath-verify');
});

Route::get('/switch-language/{locale}', function ($locale) {
    // Ensure the locale is valid before setting it (optional but recommended)
    $validLocales = ['en', 'ar'];  // Add your valid locales here

    if (in_array($locale, $validLocales)) {
        session(['locale' => $locale]);  // Set the locale in the session
        app()->setLocale($locale);       // Set the application's locale
    }

    return back();  // Redirect back to the previous page
})->name('switch.language');

Route::get('share/property', function (Request $request) {
    $propertyId = $request->query('id');

    // Append propertyId to App Store tracking link if you have one
    $storeLink = 'https://play.google.com/store/apps/details?id=sa.kera.app';

    return redirect()->away($storeLink);
});

// Custom Error Routes
Route::middleware([\App\Http\Middleware\ErrorPageMiddleware::class])->group(function () {
    Route::get('/errors/404', [\App\Http\Controllers\ErrorController::class, 'error404'])->name('errors.404');
    Route::get('/errors/403', [\App\Http\Controllers\ErrorController::class, 'error403'])->name('errors.403');
    Route::get('/errors/419', [\App\Http\Controllers\ErrorController::class, 'error419'])->name('errors.419');
    Route::get('/errors/500', [\App\Http\Controllers\ErrorController::class, 'error500'])->name('errors.500');
    Route::get('/errors/503', [\App\Http\Controllers\ErrorController::class, 'error503'])->name('errors.503');
    Route::get('/errors/minimal', [\App\Http\Controllers\ErrorController::class, 'errorMinimal'])->name('errors.minimal');
});
