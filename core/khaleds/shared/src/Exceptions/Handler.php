<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Only redirect to custom error pages for web requests
        if ($request->expectsJson()) {
            return parent::render($request, $e);
        }

        // Handle specific HTTP exceptions
        if ($e instanceof NotFoundHttpException) {
            return redirect()->route('errors.404');
        }

        if ($e instanceof AccessDeniedHttpException) {
            return redirect()->route('errors.403');
        }

        if ($e instanceof TokenMismatchException) {
            return redirect()->route('errors.419');
        }

        if ($e instanceof HttpException) {
            $statusCode = $e->getStatusCode();

            switch ($statusCode) {
                case 404:
                    return redirect()->route('errors.404');
                case 403:
                    return redirect()->route('errors.403');
                case 419:
                    return redirect()->route('errors.419');
                case 500:
                    return redirect()->route('errors.500');
                case 503:
                    return redirect()->route('errors.503');
                default:
                    return redirect()->route('errors.minimal');
            }
        }

        // For 500 errors and other server errors
        if ($this->isHttpException($e) === false && config('app.debug') === false) {
            return redirect()->route('errors.500');
        }

        return parent::render($request, $e);
    }

    //todo
    /*todo
     * make custom handlar if parent exception from our type throw custom validation and
     * be rollback from database
     *
     * create group of middle ware if you need
     * */
}
