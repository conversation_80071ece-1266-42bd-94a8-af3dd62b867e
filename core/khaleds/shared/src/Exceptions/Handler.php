<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     */
    public function render($request, Throwable $e)
    {
        // Only handle custom error pages for web requests
        if ($request->expectsJson()) {
            return parent::render($request, $e);
        }

        // Set proper locale for error pages
        $this->setErrorPageLocale($request);

        // Handle specific HTTP exceptions
        if ($e instanceof NotFoundHttpException) {
            return response()->view('errors.404', [
                'exception' => $e,
                'request' => $request
            ], 404);
        }

        if ($e instanceof AccessDeniedHttpException) {
            return response()->view('errors.403', [
                'exception' => $e,
                'request' => $request
            ], 403);
        }

        if ($e instanceof TokenMismatchException) {
            return response()->view('errors.419', [
                'exception' => $e,
                'request' => $request
            ], 419);
        }

        if ($e instanceof HttpException) {
            $statusCode = $e->getStatusCode();

            switch ($statusCode) {
                case 404:
                    return response()->view('errors.404', [
                        'exception' => $e,
                        'request' => $request
                    ], 404);
                case 403:
                    return response()->view('errors.403', [
                        'exception' => $e,
                        'request' => $request
                    ], 403);
                case 419:
                    return response()->view('errors.419', [
                        'exception' => $e,
                        'request' => $request
                    ], 419);
                case 500:
                    return response()->view('errors.500', [
                        'exception' => $e,
                        'request' => $request
                    ], 500);
                case 503:
                    return response()->view('errors.503', [
                        'exception' => $e,
                        'request' => $request
                    ], 503);
                default:
                    return response()->view('errors.minimal', [
                        'exception' => $e,
                        'request' => $request
                    ], $statusCode);
            }
        }

        // For 500 errors and other server errors
        if ($this->isHttpException($e) === false && config('app.debug') === false) {
            return response()->view('errors.500', [
                'exception' => $e,
                'request' => $request
            ], 500);
        }

        return parent::render($request, $e);
    }

    /**
     * Set proper locale for error pages
     */
    private function setErrorPageLocale($request)
    {
        if (session()->has('locale')) {
            app()->setLocale(session()->get('locale'));
        } elseif (auth()->check() && auth()->user()->lang) {
            app()->setLocale(auth()->user()->lang);
        } else {
            app()->setLocale(config('app.locale', 'en'));
        }
    }

    //todo
    /*todo
     * make custom handlar if parent exception from our type throw custom validation and
     * be rollback from database
     *
     * create group of middle ware if you need
     * */
}
